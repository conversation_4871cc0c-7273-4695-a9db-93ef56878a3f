import 'package:flutter/cupertino.dart';
import 'package:flutter/foundation.dart';
import 'package:room_eight/core/api_config/endpoints/socket_key.dart';
import 'package:room_eight/core/socket/socket_service.dart';
import 'package:room_eight/core/utils/app_exports.dart';
import 'package:room_eight/core/utils/loading_animation_widget.dart';
import 'package:room_eight/core/utils/string_extension.dart';
import 'package:room_eight/views/chat/bloc/chat_bloc.dart';
import 'package:room_eight/views/chat/model/chat_message_list_model.dart';
import 'package:room_eight/views/chat/model/search_user_model.dart';
import 'package:room_eight/views/chat/widget/date_time_utils.dart';
import 'package:room_eight/views/chat/widget/link_preview.dart';
import 'package:room_eight/views/chat/widget/typing_indicator_widget.dart';
import 'package:room_eight/views/chat/widget/user_search_dialog.dart';
import 'package:room_eight/widgets/custom_widget/custom_debounce.dart';

enum TypeWriterStatus { typing, typed }

class ChatScreen extends StatefulWidget {
  final dynamic args;

  const ChatScreen({super.key, this.args});
  static Widget builder(BuildContext context) {
    var args = ModalRoute.of(context)?.settings.arguments;
    return ChatScreen(args: args);
    // BlocProvider<ChatBloc>(
    //   create: (context) =>
    //       ChatBloc(const ChatState(), ChatRepository(apiClient: ApiClient()))
    //         ..add(ChatInitialEvent()),
    //   child: ChatScreen(args: args),
    // );
  }

  /// Static method to start a chat with a user by their token/username
  static void startChatWithUserToken(BuildContext context, String userToken) {
    final navigator = Navigator.of(context);
    final scaffoldMessenger = ScaffoldMessenger.of(context);
    final chatBloc = context.read<ChatBloc>();

    // Show loading dialog while searching for user
    showDialog(
      context: context,
      barrierDismissible: false,
      builder: (context) => const Center(child: CircularProgressIndicator()),
    );

    // Search for user by token/username
    chatBloc.add(SearchUserListEvent(searchtext: userToken));

    // Listen for search results
    StreamSubscription? subscription;
    subscription = chatBloc.stream.listen((state) {
      if (!state.searchuserListLoading && state.searchuserList.isNotEmpty) {
        // User found, close loading dialog
        navigator.pop();

        // Get the first matching user
        final user = state.searchuserList.first;

        // Navigate to chat screen with the found user
        NavigatorService.pushNamed(
          AppRoutes.chatscreen,
          arguments: [
            user,
            () {}, // Empty callback function
          ],
        );

        // Cancel subscription
        subscription?.cancel();
      } else if (!state.searchuserListLoading && state.searchuserList.isEmpty) {
        // User not found, close loading dialog and show error
        navigator.pop();
        scaffoldMessenger.showSnackBar(
          SnackBar(
            content: Text('User with token "$userToken" not found'),
            backgroundColor: Colors.red,
          ),
        );

        // Cancel subscription
        subscription?.cancel();
      }
    });

    // Cancel subscription after 10 seconds to prevent memory leaks
    Timer(const Duration(seconds: 10), () {
      subscription?.cancel();
    });
  }

  /// Static method to start a chat directly with a SearchUserData object
  static void startChatWithUser(
    BuildContext context,
    SearchUserData user, {
    VoidCallback? onBack,
  }) {
    NavigatorService.pushNamed(
      AppRoutes.chatscreen,
      arguments: [user, onBack ?? () {}],
    );
  }

  /// Static method to search for users and show results in a dialog
  static void showUserSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => const UserSearchDialog(),
    );
  }

  @override
  State<ChatScreen> createState() => _ChatScreenState();
}

class _ChatScreenState extends State<ChatScreen> {
  ScrollController? _scrollController;
  bool _showScrollToBottomButton = false;
  final ValueNotifier<String> _inputText = ValueNotifier('');
  ValueNotifier<TypeWriterStatus> composingStatus = ValueNotifier(
    TypeWriterStatus.typed,
  );

  // RecorderController? controller;
  ValueNotifier<bool> isRecording = ValueNotifier(false);
  late Debouncer debouncer;
  final ValueNotifier<bool> _showTypingIndicator = ValueNotifier(false);
  ValueListenable<bool> get typingIndicatorNotifier => _showTypingIndicator;
  int lastMassageID = 0;

  bool _isStart = true;

  @override
  void initState() {
    SocketService.emit(SocketConfig.joinSocket, {
      'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
    });
    SocketService.response(SocketConfig.joinSocket, (joinSocket) {
      Logger.lOG(joinSocket);
    });
    debouncer = Debouncer(const Duration(seconds: 1));
    super.initState();
    _scrollController = ScrollController()..addListener(_scrollListener);

    // Set up socket listeners once in initState
    _setupSocketListeners();

    // Debug widget.args
    Logger.lOG("ChatScreen initState - widget.args: ${widget.args}");
    Logger.lOG(
      "ChatScreen initState - widget.args type: ${widget.args.runtimeType}",
    );
    if (widget.args != null) {
      Logger.lOG(
        "ChatScreen initState - widget.args length: ${widget.args.length}",
      );
      if (widget.args.length > 0) {
        Logger.lOG("ChatScreen initState - widget.args[0]: ${widget.args[0]}");
        Logger.lOG(
          "ChatScreen initState - widget.args[0] type: ${widget.args[0].runtimeType}",
        );
        if (widget.args[0] != null) {
          Logger.lOG(
            "ChatScreen initState - widget.args[0].userId: ${widget.args[0].userId}",
          );
        }
      }
    }

    // Check if args exist and have the required data
    if (widget.args != null &&
        widget.args.length > 0 &&
        widget.args[0] != null &&
        widget.args[0].userId != null) {
      Logger.lOG(
        "ChatScreen initState - Calling GetChatMessageListEvent with userId: ${widget.args[0].userId}",
      );
      scheduleMicrotask(
        () => context.read<ChatBloc>().add(
          GetChatMessageListEvent(
            page: 1,
            userId: widget.args[0].userId.toString(),
          ),
        ),
      );
    } else {
      // Handle case where no arguments are provided
      Logger.lOG(
        "ChatScreen opened without proper arguments - widget.args: ${widget.args}",
      );
    }

    if (defaultTargetPlatform == TargetPlatform.iOS ||
        defaultTargetPlatform == TargetPlatform.android) {
      // controller = RecorderController();
    }
    _scrollController?.addListener(() {
      if (_scrollController!.offset > 300) {
        // If the user scrolls up 300px
        setState(() {
          _showScrollToBottomButton = true;
        });
      } else {
        setState(() {
          _showScrollToBottomButton = false;
        });
      }
    });
  }

  void _setupSocketListeners() {
    // Set up socket listeners for receiving messages
    SocketService.response(SocketConfig.isTyping, (response) {
      if (mounted) {
        _showTypingIndicator.value = response['is_typing'];
      }
    });

    SocketService.response(SocketConfig.receivemessage, (response) {
      if (mounted) {
        final id = response['id'];
        final message = response['message'];
        final type = response['type'];
        final createdat = response['created_at'];
        final sentby = response['sent_by'];

        Logger.lOG(
          "Received message via socket: $message with ID: $id from user: $sentby",
        );

        // Only add the message if it's not from the current user (to avoid duplicates with sent messages)
        // and if it doesn't already exist in the list
        final currentUserId = Prefobj.preferences?.get(Prefkeys.USER_ID);
        if (sentby != currentUserId) {
          context.read<ChatBloc>().add(
            UpdateChatMessageSocketEvent(
              id: id,
              createdat: createdat,
              message: message,
              sentby: sentby,
              type: type,
            ),
          );
        }

        if (_isStart) {
          Future.delayed(Duration(seconds: 2), () {
            if (sentby != Prefobj.preferences?.get(Prefkeys.USER_ID)) {
              Logger.lOG("touser $touser");
              SocketService.emit(SocketConfig.messageRead, {
                'Authorization': Prefobj.preferences?.get(Prefkeys.AUTHTOKEN),
                'to': Prefobj.preferences?.get(Prefkeys.USER_ID),
                'message_id': lastMassageID,
              });
            }
          });
          _isStart = false;
        }
        setState(() {});
      }
    });

    // Listen to sendmessage for confirmation and replace temporary message with real one
    SocketService.response(SocketConfig.sendmessage, (response) {
      if (mounted) {
        final id = response['id'];
        final message = response['message'];
        final type = response['type'];
        final createdat = response['created_at'];
        final sentby = response['sent_by'];

        Logger.lOG("Message sent confirmation: $message with ID: $id");

        // Remove any temporary message with the same content and replace with real message
        final currentState = context.read<ChatBloc>().state;
        final updatedMessageList = List<ChatMessageData>.from(
          currentState.chatMessageList,
        );

        // Remove temporary message (if exists) and add the real one
        updatedMessageList.removeWhere(
          (msg) =>
              msg.message == message &&
              msg.sentBy == sentby &&
              msg.id != id && // Don't remove if it's already the real message
              msg.id.toString().length >
                  10, // Temporary IDs are timestamps (longer)
        );

        // Add the real message if it doesn't already exist
        final realMessageExists = updatedMessageList.any((msg) => msg.id == id);
        if (!realMessageExists) {
          final realMessage = ChatMessageData(
            id: id,
            createdAt: createdat,
            message: message,
            sentBy: sentby,
            type: type,
          );
          updatedMessageList.insert(0, realMessage);
        }

        // Update the state with the cleaned list using an event
        context.read<ChatBloc>().add(
          UpdateChatMessageListDirectEvent(messageList: updatedMessageList),
        );

        setState(() {});
      }
    });
  }

  @override
  void dispose() {
    _scrollController?.dispose();
    _isStart = false;
    super.dispose();
  }

  @override
  void didChangeDependencies() {
    super.didChangeDependencies();

    // Fallback: Try to load messages if not already loaded
    final currentState = context.read<ChatBloc>().state;
    if (currentState.chatMessageList.isEmpty && !currentState.isloding) {
      Logger.lOG("didChangeDependencies - Attempting fallback API call");

      if (widget.args != null &&
          widget.args.length > 0 &&
          widget.args[0] != null &&
          widget.args[0].userId != null) {
        Logger.lOG(
          "didChangeDependencies - Calling GetChatMessageListEvent with userId: ${widget.args[0].userId}",
        );
        context.read<ChatBloc>().add(
          GetChatMessageListEvent(
            page: 1,
            userId: widget.args[0].userId.toString(),
          ),
        );
      } else {
        Logger.lOG(
          "didChangeDependencies - No valid args found, trying with test userId",
        );
        // Try with a test userId for debugging
        context.read<ChatBloc>().add(
          GetChatMessageListEvent(page: 1, userId: "1"),
        );
      }
    }
  }

  void _scrollListener() {
    if (_scrollController?.position.pixels ==
        _scrollController?.position.maxScrollExtent) {
      final state = context.read<ChatBloc>().state;
      if (!state.isLoadingMore) {
        context.read<ChatBloc>().add(
          GetChatMessageListEvent(
            page: state.page + 1,
            userId: widget.args.userId.toString(),
          ),
        );
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    isMessageScreen = true;

    return WillPopScope(
      onWillPop: () async {
        context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
        NavigatorService.goBack();
        if (widget.args != null &&
            widget.args.length > 1 &&
            widget.args[1] != null) {
          widget.args[1]();
        }
        FocusScope.of(context).requestFocus(FocusNode());
        isMessageScreen = false;
        massageUserID = 0;
        touser = 0;
        // context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
        // try {
        //   await widget.args[1]();
        //   context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
        // } catch (e) {
        //   debugPrint("Error in args[1]: $e");
        // }
        return true;
      },
      child: BlocBuilder<ChatBloc, ChatState>(
        builder: (context, state) {
          Logger.lOG(
            "ChatBloc state updated - Messages: ${state.chatMessageList.length}, Loading: ${state.isloding}",
          );
          return BlocBuilder<ThemeBloc, ThemeState>(
            builder: (context, themeState) {
              // only first time call this socket

              Future.delayed(Duration(seconds: 2), () {
                if (touser == Prefobj.preferences?.get(Prefkeys.USER_ID)) {
                  Logger.lOG("touser $touser");
                  SocketService.emit(SocketConfig.messageRead, {
                    'Authorization': Prefobj.preferences?.get(
                      Prefkeys.AUTHTOKEN,
                    ),
                    'to': touser,
                    'message_id': state.chatMessageList.first.id,
                  });
                  // context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
                }
              });
              return Scaffold(
                body: SafeArea(
                  child: Stack(
                    alignment: Alignment.bottomCenter / 1.3.h,
                    children: [
                      Column(
                        children: [
                          _buildchatAppBar(
                            context,
                            themeState,
                            widget.args?[0],
                          ),
                          Expanded(
                            child: InkWell(
                              focusColor: Colors.transparent,
                              onTap: () {
                                FocusManager.instance.primaryFocus?.unfocus();
                              },
                              child: Padding(
                                padding: EdgeInsets.symmetric(
                                  horizontal: 20.0.w,
                                ),
                                child: Column(
                                  children: [
                                    Visibility(
                                      visible: state.isLoadingMore,
                                      child: SizedBox(
                                        height: 50.h,
                                        child: Center(
                                          child: CupertinoActivityIndicator(
                                            color: Theme.of(
                                              context,
                                            ).colorScheme.primary,
                                          ),
                                        ),
                                      ),
                                    ),
                                    Expanded(
                                      child: Column(
                                        children: [
                                          Expanded(
                                            child: state.isloding
                                                ? const LoadingAnimationWidget()
                                                : _buildMessageList(
                                                    state.chatMessageList,
                                                    themeState,
                                                  ),
                                          ),
                                        ],
                                      ),
                                    ),
                                    buildSizedBoxH(20.0),
                                    ValueListenableBuilder(
                                      valueListenable: typingIndicatorNotifier,
                                      builder: (context, value, child) =>
                                          TypingIndicator(
                                            showIndicator: value,
                                            userrname:
                                                widget.args?[0]?.userName
                                                    ?.toString() ??
                                                'User',
                                          ),
                                    ),
                                    _buildMessageTextField(state, themeState),
                                    Platform.isIOS
                                        ? buildSizedBoxH(30.0)
                                        : buildSizedBoxH(20.0),
                                  ],
                                ),
                              ),
                            ),
                          ),
                        ],
                      ),
                      if (_showScrollToBottomButton)
                        FloatingActionButton(
                          mini: true,
                          backgroundColor: Theme.of(context).primaryColor,
                          onPressed: () {
                            _scrollToBottom();
                          },
                          child: const Icon(Icons.arrow_downward),
                        ),
                    ],
                  ),
                ),
              );
            },
          );
        },
      ),
    );
  }

  PreferredSizeWidget _buildchatAppBar(
    BuildContext context,
    ThemeState themeState,
    dynamic args,
  ) {
    return AppBar(
      backgroundColor: Colors.transparent,
      surfaceTintColor: Colors.transparent,
      automaticallyImplyLeading: false,
      leadingWidth: 30.0.w,
      leading: InkWell(
        onTap: () {
          context.read<ChatBloc>().add(const GetChatListEvent(page: 1));
          NavigatorService.goBack();
          if (widget.args != null &&
              widget.args.length > 1 &&
              widget.args[1] != null) {
            widget.args[1]();
          }
          FocusScope.of(context).requestFocus(FocusNode());
          isMessageScreen = false;
          massageUserID = 0;
          touser = 0;
        },
        child: CustomImageView(
          imagePath: Assets.images.svgs.icons.icBackArrow.path,
          height: 16.0.h,
          margin: EdgeInsets.only(top: 19.0.h, bottom: 19.0.w, left: 10.0.w),
        ),
      ),
      centerTitle: false,
      title: InkWell(
        onTap: () {
          // PersistentNavBarNavigator.pushNewScreen(context,
          //     screen: BlocProvider<UserProfileIdBloc>(
          //       create: (context) => UserProfileIdBloc(
          //         const UserProfileIdState(),
          //         ProfileRepository(apiClient: ApiClient()),
          //       )..add(UserProfileIdInitial()),
          //       child: UserProfileIdScreen(
          //           userId: widget.args[0].userId.toString(),
          //           stackonScreen: true),
          //     ));
        },
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: CustomImageView(
                  radius: BorderRadius.circular(25.r),
                  height: 40.0.h,
                  width: 40.0.w,
                  fit: BoxFit.cover,
                  imagePath:
                      args?.profileImage.toString() == "" ||
                          args?.profileImage.toString() == null
                      ? Assets.images.pngs.other.pngAppLogo.path
                      : "${SocketConfig.mainbaseURL}${args?.profileImage.toString()}",
                  alignment: Alignment.center,
                ),
              ),
            ),
            buildSizedboxW(8),
            Column(
              mainAxisAlignment: MainAxisAlignment.start,
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  args?.userName?.toString() ?? 'User',
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w700,
                    fontSize: 16.0.sp,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildMessageList(
    List<ChatMessageData> messages,
    ThemeState themeState,
  ) {
    Logger.lOG("Building message list with ${messages.length} messages");

    if (messages.isEmpty) {
      return Center(
        child: Text(
          'No messages yet',
          style: Theme.of(context).textTheme.bodyMedium?.copyWith(
            color: Theme.of(context).iconTheme.color,
          ),
        ),
      );
    }

    final groupedMessages = _groupMessagesByDate(messages);
    Logger.lOG("Grouped messages into ${groupedMessages.length} date groups");

    return ListView.builder(
      padding: EdgeInsets.zero,
      controller: _scrollController,
      itemCount: groupedMessages.length,
      reverse: true,
      shrinkWrap: true,
      itemBuilder: (context, index) {
        final date = groupedMessages.keys.elementAt(index);
        final messages = groupedMessages[date]!;

        return Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Padding(
                  padding: const EdgeInsets.all(8.0),
                  child: Text(
                    date.toString(),
                    style: Theme.of(
                      context,
                    ).textTheme.headlineSmall?.copyWith(fontSize: 11.0.sp),
                  ),
                ),
              ],
            ),
            ListView.builder(
              physics: const NeverScrollableScrollPhysics(),
              shrinkWrap: true,
              reverse: true,
              itemCount: messages.length,
              itemBuilder: (context, messageIndex) {
                lastMassageID = messages.first.id ?? 0;
                return _buildChatBubble(messages[messageIndex], themeState);
              },
            ),
          ],
        );
      },
    );
  }

  void _scrollToBottom() {
    _scrollController?.animateTo(
      0.0, // Scroll to the top as the list is reversed
      duration: const Duration(milliseconds: 300),
      curve: Curves.easeInOut,
    );
  }

  Widget _buildChatBubble(ChatMessageData message, ThemeState themeState) {
    final currentUserId = Prefobj.preferences?.get(Prefkeys.USER_ID);
    final messageSentBy = message.sentBy;
    final isCurrentUser =
        messageSentBy != null && messageSentBy == currentUserId;

    Logger.lOG(
      "Message sentBy: $messageSentBy, Current User ID: $currentUserId, Are they equal: $isCurrentUser",
    );

    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 5.0),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.end,
        mainAxisAlignment: isCurrentUser
            ? MainAxisAlignment.end
            : MainAxisAlignment.start,
        children: [
          Visibility(
            visible: !isCurrentUser,
            child: ClipRRect(
              clipBehavior: Clip.hardEdge,
              child: Padding(
                padding: const EdgeInsets.all(4.0),
                child: CustomImageView(
                  radius: BorderRadius.circular(45.r),
                  border: Border.all(
                    color: Theme.of(context).customColors.primaryColor!,
                  ),
                  height: 45.0.h,
                  width: 45.0.w,
                  fit: BoxFit.cover,
                  imagePath:
                      widget.args[0]?.profileImage == null ||
                          widget.args[0]?.profileImage == ''
                      ? Assets.images.pngs.other.pngAppLogo.path
                      : "${SocketConfig.mainbaseURL}${widget.args[0]?.profileImage.toString()}",
                  alignment: Alignment.center,
                ),
              ),
            ),
          ),
          ConstrainedBox(
            constraints: BoxConstraints(maxWidth: 280.w),
            child: Container(
              constraints: BoxConstraints(
                maxWidth: MediaQuery.of(context).size.width * 0.75,
              ),
              padding: EdgeInsets.symmetric(
                horizontal: message.type == 'image' ? 0.w : 12.w,
                vertical: message.type == 'image' ? 0.h : 10.h,
              ),
              margin: EdgeInsets.fromLTRB(5.w, 0.h, 6.w, 2.h),
              decoration: BoxDecoration(
                color: isCurrentUser
                    ? Theme.of(context).primaryColor
                    : ThemeData().customColors.primaryColor,
                borderRadius: BorderRadius.only(
                  topLeft: Radius.circular(isCurrentUser ? 15.r : 0.r),
                  topRight: Radius.circular(15.r),
                  bottomRight: Radius.circular(isCurrentUser ? 0.r : 15.r),
                  bottomLeft: Radius.circular(15.r),
                ),
              ),
              child: message.type == 'image'
                  ? InkWell(
                      onTap: () {
                        FocusScope.of(context).unfocus();
                        // PersistentNavBarNavigator.pushNewScreen(context,
                        //     screen: FlowkarImagePreview(
                        //       imagepath:
                        //           '${APIEndPoints.mainbaseURL}/${message.message}',
                        //     ));
                      },
                      child: CustomImageView(
                        imagePath:
                            '${SocketConfig.mainbaseURL}/${message.message}',
                        height: 250.0.h,
                        width: 150.0.w,
                        radius: BorderRadius.circular(4.0.r),
                        fit: BoxFit.cover,
                      ),
                    )
                  : message.type == 'custom'
                  ? SizedBox.shrink()
                  : message.message!.isUrl
                  ? LinkPreview(url: message.message ?? '')
                  : Text(
                      message.message ?? '',
                      textAlign: TextAlign.start,
                      style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                        color: isCurrentUser
                            ? ThemeData().customColors.fillColor
                            : ThemeData().customColors.blackColor,
                        fontSize: 16.0.sp,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
            ),
          ),
        ],
      ),
    );
  }

  Map<String, List<ChatMessageData>> _groupMessagesByDate(
    List<ChatMessageData> messages,
  ) {
    final Map<String, List<ChatMessageData>> groupedMessages = {};
    for (var message in messages) {
      try {
        if (message.createdAt != null && message.createdAt!.isNotEmpty) {
          final dateTime = DateTime.parse(
            message.createdAt.toString().split("+").first,
          );
          final formattedDate = dateTime.formatForChatMessage();
          groupedMessages.putIfAbsent(formattedDate, () => []).add(message);
        } else {
          // Handle messages with null or empty createdAt
          final fallbackDate = DateTime.now().formatForChatMessage();
          groupedMessages.putIfAbsent(fallbackDate, () => []).add(message);
        }
      } catch (e) {
        Logger.lOG("Error parsing date for message ${message.id}: $e");
        // Use current date as fallback
        final fallbackDate = DateTime.now().formatForChatMessage();
        groupedMessages.putIfAbsent(fallbackDate, () => []).add(message);
      }
    }
    return groupedMessages;
  }

  Widget _buildMessageTextField(ChatState state, ThemeState themestate) {
    return ValueListenableBuilder<bool>(
      valueListenable: isRecording,
      builder: (_, isRecordingValue, child) {
        return Row(
          children: [
            // if (isRecordingValue && controller != null && !kIsWeb)
            //   Expanded(
            //     child: Container(
            //       decoration: BoxDecoration(
            //           color: AppColors.primaryColor,
            //           borderRadius: BorderRadius.circular(22.0.r)),
            //       child: Padding(
            //         padding: const EdgeInsets.all(4.0),
            //         child: Row(
            //           mainAxisSize: MainAxisSize.min,
            //           children: [
            //             if (isRecordingValue)
            //               CustomImageView(
            //                 height: 55.0.h,
            //                 imagePath:
            //                     Assets.images.icons.icDeleteRecording.path,
            //                 onTap: () {
            //                   _cancelRecording();
            //                 },
            //               ),
            //             Expanded(
            //               child: AudioWaveforms(
            //                 size: const Size(double.infinity, 50),
            //                 recorderController: controller!,
            //                 padding: EdgeInsets.symmetric(horizontal: 8.0.w),
            //                 decoration: BoxDecoration(
            //                     borderRadius: BorderRadius.circular(12.0.r)),
            //                 waveStyle: const WaveStyle(
            //                   extendWaveform: true,
            //                   showMiddleLine: false,
            //                   waveCap: StrokeCap.round,
            //                   waveColor: AppColors.whitecolor,
            //                 ),
            //               ),
            //             ),
            //             Padding(
            //               padding: const EdgeInsets.only(right: 8.0),
            //               child: InkWell(
            //                 onTap: _recordOrStop,
            //                 child: Padding(
            //                   padding: const EdgeInsets.all(8.0),
            //                   child: Text(
            //                     Lang.of(context).lbl_send,
            //                     style: Theme.of(context)
            //                         .textTheme
            //                         .bodySmall
            //                         ?.copyWith(
            //                             color: AppColors.whitecolor,
            //                             fontWeight: FontWeight.bold),
            //                   ),
            //                 ),
            //               ),
            //             ),
            //           ],
            //         ),
            //       ),
            //     ),
            //   ),
            Visibility(
              child: Expanded(
                child: SizedBox(
                  height: 59,
                  child: CustomTextInputField(
                    controller: state.chatController,
                    hintLabel: 'Enter Message',
                    context: context,
                    onChanged: (inputText) {
                      if (widget.args?[0]?.userId != null) {
                        context.read<ChatBloc>().add(
                          TypingSocketEvent(
                            userId: widget.args![0].userId,
                            isTyping: "1",
                          ),
                        );
                      }
                      _inputText.value = inputText;
                    },
                    // prefix: ValueListenableBuilder<String>(
                    //   valueListenable: _inputText,
                    //   builder: (_, inputTextValue, __) {
                    //     if (inputTextValue.isEmpty) {
                    //       return Padding(
                    //         padding: const EdgeInsets.all(14.0),
                    //         child: CustomImageView(
                    //           color: ThemeData().customColors.white,
                    //           height: 25.h,
                    //           width: 25.w,
                    //           imagePath: '',
                    //           onTap: () => _onIconPressed(ImageSource.camera),
                    //         ),
                    //       );
                    //     } else {
                    //       return IconButton(
                    //         icon: CustomImageView(
                    //           color: ThemeData().customColors.white,
                    //           height: 12.0.h,
                    //           width: 12.0.w,
                    //           imagePath: '',
                    //         ),
                    //         onPressed: () {
                    //           state.chatController?.clear();
                    //           _inputText.value = '';
                    //         },
                    //       );
                    //     }
                    //   },
                    // ),
                    suffixIcon: ValueListenableBuilder<String>(
                      valueListenable: _inputText,
                      builder: (_, inputTextValue, __) {
                        if (inputTextValue.isNotEmpty) {
                          return Padding(
                            padding: EdgeInsets.only(right: 0.0.w),
                            child: InkWell(
                              onTap: () {
                                if (_inputText.value.isNotEmpty &&
                                    widget.args?[0]?.userId != null) {
                                  // Store the message before sending
                                  final messageToSend = _inputText.value;

                                  // Clear the text field immediately for better UX
                                  _inputText.value = '';
                                  state.chatController?.clear();

                                  // Create a temporary message for immediate UI feedback
                                  final tempMessage = ChatMessageData(
                                    id: DateTime.now()
                                        .millisecondsSinceEpoch, // Temporary ID
                                    message: messageToSend,
                                    type: 'text',
                                    sentBy: Prefobj.preferences?.get(
                                      Prefkeys.USER_ID,
                                    ),
                                    createdAt: DateTime.now().toIso8601String(),
                                  );

                                  // Add temporary message to UI immediately
                                  context.read<ChatBloc>().add(
                                    UpdateChatMessageSocketEvent(
                                      id: tempMessage.id ?? 0,
                                      createdat:
                                          tempMessage.createdAt ??
                                          DateTime.now().toIso8601String(),
                                      message: tempMessage.message ?? '',
                                      sentby:
                                          tempMessage.sentBy ??
                                          Prefobj.preferences?.get(
                                            Prefkeys.USER_ID,
                                          ) ??
                                          0,
                                      type: tempMessage.type ?? 'text',
                                    ),
                                  );

                                  // Send the message via socket
                                  context.read<ChatBloc>().add(
                                    SendMessageEvent(
                                      message: messageToSend,
                                      file: '',
                                      touserId:
                                          int.tryParse(
                                            widget.args![0].userId.toString(),
                                          ) ??
                                          0,
                                      type: 'text',
                                    ),
                                  );

                                  // Scroll to bottom to show new message
                                  Future.delayed(
                                    Duration(milliseconds: 100),
                                    () {
                                      _scrollToBottom();
                                    },
                                  );

                                  FocusScope.of(
                                    context,
                                  ).requestFocus(FocusNode());
                                }
                              },
                              child: Icon(
                                Icons.send_rounded,
                                color: Theme.of(context).primaryColor,
                              ),
                              // Text(
                              //   // "Lang.of(context).lbl_send",
                              //   style: Theme.of(context).textTheme.bodySmall?.copyWith(color: Theme.of(context).primaryColor, fontWeight: FontWeight.bold),
                              // ),
                            ),
                          );
                        } else {
                          if (widget.args?[0]?.userId != null) {
                            context.read<ChatBloc>().add(
                              TypingSocketEvent(
                                userId: widget.args![0].userId,
                                isTyping: "0",
                              ),
                            );
                          }
                          return SizedBox.shrink();
                          // Row(
                          //   mainAxisAlignment: MainAxisAlignment.end,
                          //   mainAxisSize: MainAxisSize.min,
                          //   children: [
                          //     Padding(
                          //       padding: EdgeInsets.only(
                          //           top: 18.0.h, bottom: 18.0.h, right: 8.0.w),
                          //       child: CustomImageView(
                          //         color: ThemeData().customColors.white,
                          //         imagePath: '',
                          //         height: 20.h,
                          //         width: 20.w,
                          //         onTap: () =>
                          //             _onIconPressed(ImageSource.gallery),
                          //       ),
                          //     ),
                          //     // Padding(
                          //     //   padding: EdgeInsets.only(
                          //     //       top: 15.0.h, bottom: 15.0.h, right: 15.0.w),
                          //     //   child: CustomImageView(
                          //     //       height: 25.h,
                          //     //       width: 25.0.w,
                          //     //       imagePath:
                          //     //           Assets.images.icons.icMicrophone.path,
                          //     //       onTap: () => _recordOrStop()),
                          //     // ),
                          //   ],
                          // );
                        }
                      },
                    ),
                    type: InputType.text,
                  ),
                ),
              ),
            ),
          ],
        );
      },
    );
  }

  // FutureOr<void> _cancelRecording() async {
  //   assert(
  //     defaultTargetPlatform == TargetPlatform.iOS ||
  //         defaultTargetPlatform == TargetPlatform.android,
  //     "Voice messages are only supported with android and ios platform",
  //   );
  //   if (!isRecording.value) return;
  //   // final path = await controller?.stop();
  //   if (path == null) {
  //     isRecording.value = false;
  //     return;
  //   }
  //   final file = File(path);

  //   if (await file.exists()) {
  //     await file.delete();
  //   }

  //   isRecording.value = false;
  // }

  // Future<void> _recordOrStop() async {
  //   assert(
  //     defaultTargetPlatform == TargetPlatform.iOS ||
  //         defaultTargetPlatform == TargetPlatform.android,
  //     "Voice messages are only supported with android and ios platform",
  //   );
  //   if (!isRecording.value) {
  //     await controller?.record(
  //       androidEncoder: AndroidEncoder.aac,
  //       iosEncoder: IosEncoder.kAudioFormatMPEG4AAC,
  //       androidOutputFormat: AndroidOutputFormat.mpeg4,
  //       bitRate: 128000,
  //       sampleRate: 44100,
  //     );
  //     isRecording.value = true;
  //   } else {
  //     final path = await controller?.stop();
  //     isRecording.value = false;
  //     path;
  //     Logger.lOG(path);
  //     // Check if an image is selected
  //     if (path != null && path.isNotEmpty) {
  //       // Get the MIME type of the image file
  //       String? mimeType = lookupMimeType(path);

  //       if (mimeType != null) {
  //         // Convert the image file to Base64
  //         File audioFile = File(path);
  //         List<int> audioBytes = await audioFile.readAsBytes();
  //         String base64audio = base64Encode(audioBytes);

  //         // Create the Data URI string
  //         String dataUri = 'data:audio/m4a;;base64,$base64audio';

  //         // Send the Data URI as a chat message

  //         context.read<ChatBloc>().add(SendMessageEvent(
  //               file: dataUri,
  //               message: "",
  //               touserId: int.tryParse(widget.args[0].userId.toString()) ?? 0,
  //               type: 'custom', // or any other type indicating an image
  //             ));
  //       } else {
  //         Logger.lOG('Could not determine MIME type.');
  //       }
  //     }
  //   }
  // }
}
